import hre from "hardhat";

async function main() {
  const MockDex = await hre.ethers.getContractFactory("MockDex");

  const dexBuy = await MockDex.deploy(); // Deploy without arguments
  await dexBuy.deployed();
  console.log("🟢 MockDexBuy deployed at:", dexBuy.address);

  const dexSell = await MockDex.deploy(); // Deploy without arguments
  await dexSell.deployed();
  console.log("🔴 MockDexSell deployed at:", dexSell.address);
}

main().catch((err) => {
  console.error("❌ Deployment failed:", err);
  process.exit(1);
});
