// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;

//Arbitrage.sol


import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "./IDex.sol";

contract Arbitrage {
    address public owner;

    event SwapOutcome(bool profitable, uint amountIn, uint amountOutSell);

    constructor() {
        owner = msg.sender;
    }

   function executeMultiHopSwap(
    address[] calldata dexes,
    address[] calldata path,
    uint amountIn
) external {
    require(msg.sender == owner, "Not authorized");

    uint amount = amountIn;
for (uint i = 0; i < dexes.length; i++) {
    address tokenFrom = path[i];
    address tokenTo = path[i + 1];

    IERC20(tokenFrom).approve(dexes[i], amount);
    amount = IDex(dexes[i]).swap(tokenFrom, tokenTo, amount);
}

event MultiHopOutcome(bool profitable, uint amountIn, uint amountOut);

}
 /**
     * @dev Simulates the arbitrage trade without executing it
     * @param dexBuy The DEX to buy tokenOut with tokenIn
     * @param dexSell The DEX to sell tokenOut for tokenIn
     * @param tokenIn The input token address
     * @param tokenOut The intermediate token address
     * @param amountIn The amount of tokenIn to trade
     * @return The expected amount of tokenIn we would get back
     */
    function getExpectedOutput(
        address dexBuy,
        address dexSell,
        address tokenIn,
        address tokenOut,
        uint amountIn
    ) external view returns (uint) {
        // Step 1: Get quote from dexBuy for tokenIn -> tokenOut
        uint amountOutBuy = IDex(dexBuy).getAmountOut(tokenIn, tokenOut, amountIn);
        
        // Step 2: Get quote from dexSell for tokenOut -> tokenIn
        uint amountOutSell = IDex(dexSell).getAmountOut(tokenOut, tokenIn, amountOutBuy);
        
        // Return the final amount of tokenIn we would receive
        return amountOutSell;
    }

    function getOwner() public view returns (address) {
        return owner;
    }
}