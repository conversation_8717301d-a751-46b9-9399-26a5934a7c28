// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;

//Arbitrage.sol


import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "./IDex.sol";

contract Arbitrage {
    address public owner;

    event SwapOutcome(bool profitable, uint amountIn, uint amountOutSell);

    constructor() {
        owner = msg.sender;
    }

   function executeSwap(
    address dexBuy,
    address dexSell,
    address tokenIn,
    address tokenOut,
    uint amountIn
) external {
    require(msg.sender == owner, "Not authorized");

    // Step 1: Pull TokenIn from caller
    IERC20(tokenIn).transferFrom(msg.sender, address(this), amountIn);

    // Step 2: Approve dexBuy to spend TokenIn
    IERC20(tokenIn).approve(dexBuy, amountIn);

    // Step 3: Execute first swap
    uint amountOutBuy = IDex(dexBuy).swap(tokenIn, tokenOut, amountIn);

    // Step 4: Approve dexSell to spend TokenOut
    IERC20(tokenOut).approve(dexSell, amountOutBuy);

    // Step 5: Execute second swap
    uint amountOutSell = IDex(dexSell).swap(tokenOut, tokenIn, amountOutBuy);

    // Step 6: Emit outcome and enforce profit
    bool profitable = amountOutSell > amountIn;
    emit SwapOutcome(profitable, amountIn, amountOutSell);

    // Step 7: Transfer the final tokens back to the caller
    IERC20(tokenIn).transfer(msg.sender, amountOutSell);


    require(profitable, "No profit");
}
    /**
     * @dev Simulates the arbitrage trade without executing it
     * @param dexBuy The DEX to buy tokenOut with tokenIn
     * @param dexSell The DEX to sell tokenOut for tokenIn
     * @param tokenIn The input token address
     * @param tokenOut The intermediate token address
     * @param amountIn The amount of tokenIn to trade
     * @return The expected amount of tokenIn we would get back
     */
    function getExpectedOutput(
        address dexBuy,
        address dexSell,
        address tokenIn,
        address tokenOut,
        uint amountIn
    ) external view returns (uint) {
        // Step 1: Get quote from dexBuy for tokenIn -> tokenOut
        uint amountOutBuy = IDex(dexBuy).getAmountOut(tokenIn, tokenOut, amountIn);
        
        // Step 2: Get quote from dexSell for tokenOut -> tokenIn
        uint amountOutSell = IDex(dexSell).getAmountOut(tokenOut, tokenIn, amountOutBuy);
        
        // Return the final amount of tokenIn we would receive
        return amountOutSell;
    }

    function getOwner() public view returns (address) {
        return owner;
    }
}