//simpleSwapFixed.js
import { ethers } from "ethers";
import { ERC20_ABI } from "../lib/erc20Abi.js";

export async function runStrategy({ arbitrage, addresses, signer }) {
  const amountIn = ethers.parseUnits("1.0", 18);

  const tokenIn = new ethers.Contract(addresses.tokenIn, ERC20_ABI, signer);
  const tokenOut = new ethers.Contract(addresses.tokenOut, ERC20_ABI, signer);

  // Log pre-swap balances
  const balanceInBefore = await tokenIn.balanceOf(signer.address);
  const balanceOutBefore = await tokenOut.balanceOf(signer.address);
  console.log("📊 Pre-swap balances:");
  console.log("   TokenIn:", ethers.formatUnits(balanceInBefore, 18));
  console.log("   TokenOut:", ethers.formatUnits(balanceOutBefore, 18));

  console.log("🔑 Using signer:", signer.address);

  // Get current nonce explicitly
  let currentNonce = await signer.provider.getTransactionCount(signer.address, 'pending');
  console.log("🔢 Current nonce before approval:", currentNonce);

  // Approve if needed
  let allowance = await tokenIn.allowance(signer.address, addresses.arbitrage);
  if (allowance < amountIn) {
    console.log("🔐 Approving tokenIn for arbitrage contract...");
    
    const approveTx = await tokenIn.approve(addresses.arbitrage, amountIn, {
      nonce: currentNonce
    });
    console.log("📝 Approval tx sent with nonce:", currentNonce);
    
    await approveTx.wait();
    console.log("✅ Approval confirmed");
    
    // Increment nonce for next transaction
    currentNonce++;

    allowance = await tokenIn.allowance(signer.address, addresses.arbitrage);
    console.log("🔍 New allowance:", ethers.formatUnits(allowance, 18));
    if (allowance < amountIn) {
      throw new Error("❌ Approval failed: allowance still insufficient");
    }
  }

  // Get fresh nonce for executeSwap
  currentNonce = await signer.provider.getTransactionCount(signer.address, 'pending');
  console.log("🔢 Current nonce before executeSwap:", currentNonce);

  // Execute swap with explicit nonce
  const tx = await arbitrage.executeSwap(
    addresses.dexBuy,
    addresses.dexSell,
    addresses.tokenIn,
    addresses.tokenOut,
    amountIn,
    {
      nonce: currentNonce
    }
  );

  console.log("📦 Strategy: simpleSwap");
  console.log("🚀 Swap tx sent:", tx.hash, "with nonce:", currentNonce);

  const receipt = await tx.wait();
  console.log("✅ Swap confirmed in block:", receipt.blockNumber);

  // Log post-swap balances
  const balanceInAfter = await tokenIn.balanceOf(signer.address);
  const balanceOutAfter = await tokenOut.balanceOf(signer.address);
  console.log("📊 Post-swap balances:");
  console.log("   TokenIn:", ethers.formatUnits(balanceInAfter, 18));
  console.log("   TokenOut:", ethers.formatUnits(balanceOutAfter, 18));
  
  // Calculate profit
  const profit = balanceInAfter - balanceInBefore;
  console.log("💰 Profit:", ethers.formatUnits(profit, 18), "TokenIn");
}
