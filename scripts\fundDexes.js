import hre from "hardhat";

async function main() {
  // Get the deployer account (should be the token owner)
  const [deployer] = await hre.ethers.getSigners();
  console.log("🏦 Funding DEXes with account:", deployer.address);

  // Contract addresses - you'll need to update these with your deployed addresses
  const tokenAAddress = "******************************************"; // Update with your TokenA address
  const tokenBAddress = "******************************************"; // Update with your TokenB address
  const dexBuyAddress = "******************************************"; // Update with your DexBuy address
  const dexSellAddress = "******************************************"; // Update with your DexSell address

  // Get contract instances
  const tokenA = await hre.ethers.getContractAt("MockERC20", tokenAAddress);
  const tokenB = await hre.ethers.getContractAt("MockERC20", tokenBAddress);

  // Amount to fund each DEX (e.g., 100,000 tokens)
  const fundingAmount = hre.ethers.utils.parseUnits("100000", 18);

  console.log("💰 Funding amount per DEX:", hre.ethers.utils.formatUnits(fundingAmount, 18));

  try {
    // Fund DexBuy with both tokens
    console.log("🟢 Funding DexBuy with TokenA...");
    await tokenA.transfer(dexBuyAddress, fundingAmount);
    
    console.log("🟢 Funding DexBuy with TokenB...");
    await tokenB.transfer(dexBuyAddress, fundingAmount);

    // Fund DexSell with both tokens
    console.log("🔴 Funding DexSell with TokenA...");
    await tokenA.transfer(dexSellAddress, fundingAmount);
    
    console.log("🔴 Funding DexSell with TokenB...");
    await tokenB.transfer(dexSellAddress, fundingAmount);

    // Verify balances
    console.log("\n📊 Verifying DEX balances:");
    
    const dexBuyTokenABalance = await tokenA.balanceOf(dexBuyAddress);
    const dexBuyTokenBBalance = await tokenB.balanceOf(dexBuyAddress);
    const dexSellTokenABalance = await tokenA.balanceOf(dexSellAddress);
    const dexSellTokenBBalance = await tokenB.balanceOf(dexSellAddress);

    console.log("🟢 DexBuy balances:");
    console.log("   TokenA:", hre.ethers.utils.formatUnits(dexBuyTokenABalance, 18));
    console.log("   TokenB:", hre.ethers.utils.formatUnits(dexBuyTokenBBalance, 18));
    
    console.log("🔴 DexSell balances:");
    console.log("   TokenA:", hre.ethers.utils.formatUnits(dexSellTokenABalance, 18));
    console.log("   TokenB:", hre.ethers.utils.formatUnits(dexSellTokenBBalance, 18));

    console.log("\n✅ DEX funding completed successfully!");

  } catch (error) {
    console.error("❌ Funding failed:", error);
    process.exit(1);
  }
}

main().catch((error) => {
  console.error("❌ Script failed:", error);
  process.exit(1);
});
