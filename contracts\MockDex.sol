// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "./IDex.sol";

contract MockDex is IDex {
    bool public isProfitableDex;

    constructor(bool _isProfitableDex) {
        isProfitableDex = _isProfitableDex;
    }

    function getAmountOut(address tokenIn, address tokenOut, uint amountIn) external view override returns (uint) {
        // Avoid unused parameter warnings
        tokenIn;
        tokenOut;

        if (isProfitableDex) {
            // DexSell: Give 2% more tokens to simulate profit opportunity
            return (amountIn * 102) / 100;
        } else {
            // DexBuy: Standard 1:1 swap
            return amountIn;
        }
    }

    // Internal function to calculate amount out
    function _getAmountOut(address tokenIn, address tokenOut, uint amountIn) internal view returns (uint) {
        // Avoid unused parameter warnings
        tokenIn;
        tokenOut;

        if (isProfitableDex) {
            // DexSell: Give 2% more tokens to simulate profit opportunity
            return (amountIn * 102) / 100;
        } else {
            // DexBuy: Standard 1:1 swap
            return amountIn;
        }
    }

    function swap(address tokenIn, address tokenOut, uint amountIn) external override returns (uint) {
        // Pull tokenIn from caller
        IERC20(tokenIn).transferFrom(msg.sender, address(this), amountIn);

        // Use internal function instead of external call
        uint amountOut = _getAmountOut(tokenIn, tokenOut, amountIn);

        // Send tokenOut back to caller
        IERC20(tokenOut).transfer(msg.sender, amountOut);

        return amountOut;
    }
}