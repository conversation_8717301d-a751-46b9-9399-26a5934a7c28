import hre from "hardhat";

async function main() {
  const [deployer] = await hre.ethers.getSigners();
  console.log("🚀 Deploying all contracts with account:", deployer.address);
  console.log(
    "💰 Account balance:",
    hre.ethers.utils.formatEther(await deployer.getBalance())
  );


  // Deploy Arbitrage contract
  console.log("\n⚡ Deploying Arbitrage contract...");
  const Arbitrage = await hre.ethers.deployContract("Arbitrage");
  await Arbitrage.deployed();
  console.log("⚡ Arbitrage deployed at:", Arbitrage.address);


  // Deploy tokens
  console.log("\n📦 Deploying tokens...");
  const initialSupply = hre.ethers.utils.parseUnits("1000000", 18);

  const TokenA = await hre.ethers.deployContract("MockERC20", [
    "TokenA",
    "TKA",
    initialSupply,
  ]);
  await TokenA.deployed();
  console.log("🟢 TokenA deployed at:", TokenA.address);

  const TokenB = await hre.ethers.deployContract("MockERC20", [
    "TokenB",
    "TKB",
    initialSupply,
  ]);
  await TokenB.deployed();
  console.log("🔵 TokenB deployed at:", TokenB.address);

  // Deploy MockDex contracts
  console.log("\n🏪 Deploying DEX contracts...");
  const MockDex = await hre.ethers.getContractFactory("MockDex");

  // Deploy DexBuy (standard 1:1 swap, isProfitableDex = false)
  const dexBuy = await MockDex.deploy(false);
  await dexBuy.deployed();
  console.log("🟢 DexBuy deployed at:", dexBuy.address);

  // Deploy DexSell (profitable swap with 2% bonus, isProfitableDex = true)
  const dexSell = await MockDex.deploy(true);
  await dexSell.deployed();
  console.log("🔴 DexSell deployed at:", dexSell.address);

  // Fund DEX contracts with tokens
  console.log("\n💰 Funding DEX contracts...");
  const fundingAmount = hre.ethers.utils.parseUnits("100000", 18);

  // Fund DexBuy with both tokens
  console.log("🟢 Funding DexBuy with TokenA...");
  await TokenA.transfer(dexBuy.address, fundingAmount);

  console.log("🟢 Funding DexBuy with TokenB...");
  await TokenB.transfer(dexBuy.address, fundingAmount);

  // Fund DexSell with both tokens
  console.log("🔴 Funding DexSell with TokenA...");
  await TokenA.transfer(dexSell.address, fundingAmount);

  console.log("🔴 Funding DexSell with TokenB...");
  await TokenB.transfer(dexSell.address, fundingAmount);

  // Verify balances
  console.log("\n📊 Final balances:");

  const deployerTokenABalance = await TokenA.balanceOf(deployer.address);
  const deployerTokenBBalance = await TokenB.balanceOf(deployer.address);
  const dexBuyTokenABalance = await TokenA.balanceOf(dexBuy.address);
  const dexBuyTokenBBalance = await TokenB.balanceOf(dexBuy.address);
  const dexSellTokenABalance = await TokenA.balanceOf(dexSell.address);
  const dexSellTokenBBalance = await TokenB.balanceOf(dexSell.address);

  console.log("👤 Deployer balances:");
  console.log(
    "   TokenA:",
    hre.ethers.utils.formatUnits(deployerTokenABalance, 18)
  );
  console.log(
    "   TokenB:",
    hre.ethers.utils.formatUnits(deployerTokenBBalance, 18)
  );

  console.log("🟢 DexBuy balances:");
  console.log(
    "   TokenA:",
    hre.ethers.utils.formatUnits(dexBuyTokenABalance, 18)
  );
  console.log(
    "   TokenB:",
    hre.ethers.utils.formatUnits(dexBuyTokenBBalance, 18)
  );

  console.log("🔴 DexSell balances:");
  console.log(
    "   TokenA:",
    hre.ethers.utils.formatUnits(dexSellTokenABalance, 18)
  );
  console.log(
    "   TokenB:",
    hre.ethers.utils.formatUnits(dexSellTokenBBalance, 18)
  );

  // Output deployment summary
  console.log("\n📋 Deployment Summary:");
  console.log("=====================================");
  console.log("TokenA:", TokenA.address);
  console.log("TokenB:", TokenB.address);
  console.log("DexBuy:", dexBuy.address);
  console.log("DexSell:", dexSell.address);
  console.log("Arbitrage:", Arbitrage.address);
  console.log("Owner:", deployer.address);
  console.log("=====================================");

  console.log("\n✅ All contracts deployed and funded successfully!");
}

main().catch((error) => {
  console.error("❌ Deployment failed:", error);
  process.exit(1);
});
